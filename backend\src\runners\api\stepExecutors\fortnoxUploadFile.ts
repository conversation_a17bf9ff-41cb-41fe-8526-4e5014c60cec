import axios from 'axios';
import FormData from 'form-data';
import type { FortnoxUploadFileStep } from '@rpa-project/shared/dist/esm/types/steps/api';
import { StepExecutionResult } from '../../base';
import { customerService } from '../../../services/customerService';

/**
 * Executor context for Fortnox steps
 */
export interface FortnoxExecutorContext {
  variables: Record<string, any>;
  onLog: (log: { level: 'info' | 'warn' | 'error'; message: string; stepId?: string }) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  customerId?: string;
}

/**
 * Fortnox Archive API response interface
 */
interface FortnoxArchiveResponse {
  Archive: {
    Id: string;
    Name: string;
    Size: number;
    Path: string;
  };
}

/**
 * Execute Fortnox Upload File step
 */
export async function executeFortnoxUploadFile(
  step: FortnoxUploadFileStep,
  context: FortnoxExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, customerId } = context;

  try {
    onLog({
      level: 'info',
      message: `Executing Fortnox Upload File: ${step.id}`,
      stepId: step.id
    });

    // Get Fortnox token for the customer
    if (!customerId) {
      throw new Error('Customer ID is required for Fortnox API calls');
    }

    const fortnoxTokens = await customerService.getCustomerTokensWithData(customerId);
    const fortnoxToken = fortnoxTokens.find(token => token.provider === 'Fortnox' && token.apiToken);

    if (!fortnoxToken || !fortnoxToken.apiToken) {
      throw new Error('No valid Fortnox token found for customer');
    }

    // Get base64 file content from variable
    const interpolatedInputVariable = interpolateVariables(step.inputVariable, variables);
    const base64Content = variables[interpolatedInputVariable];

    if (!base64Content) {
      throw new Error(`No file content found in variable: ${interpolatedInputVariable}`);
    }

    // Extract base64 data (remove data URL prefix if present)
    let base64Data = base64Content;
    if (typeof base64Data === 'string' && base64Data.includes(',')) {
      base64Data = base64Data.split(',')[1];
    }

    // Convert base64 to buffer
    let fileBuffer: Buffer;
    try {
      fileBuffer = Buffer.from(base64Data, 'base64');
    } catch (bufferError) {
      throw new Error(`Invalid base64 data in variable ${interpolatedInputVariable}: ${bufferError instanceof Error ? bufferError.message : 'Unknown error'}`);
    }

    onLog({
      level: 'info',
      message: `File buffer created, size: ${fileBuffer.length} bytes`,
      stepId: step.id
    });

    // Determine filename
    let filename = step.filename;
    if (!filename) {
      // Try to get filename from related variable
      const filenameVariable = `${interpolatedInputVariable}_filename`;
      filename = variables[filenameVariable] || 'uploaded_file.bin';
    }
    filename = interpolateVariables(filename, variables);

    onLog({
      level: 'info',
      message: `Uploading file: ${filename}`,
      stepId: step.id
    });

    // Create FormData for multipart upload
    const formData = new FormData();
    formData.append('file', fileBuffer, {
      filename: filename,
      contentType: 'application/octet-stream'
    });

    // Add description if provided
    if (step.description) {
      const interpolatedDescription = interpolateVariables(step.description, variables);
      formData.append('description', interpolatedDescription);
    }

    // Upload file to Fortnox Archive
    let uploadResponse;
    try {
      uploadResponse = await axios.post(
        'https://api.fortnox.se/3/archive',
        formData,
        {
          headers: {
            'Authorization': `Bearer ${fortnoxToken.apiToken}`,
            'Accept': 'application/json',
            ...formData.getHeaders()
          },
          maxContentLength: Infinity,
          maxBodyLength: Infinity
        }
      );
    } catch (apiError: any) {
      // Log detailed error information from Fortnox API
      const errorDetails = apiError.response?.data || apiError.message;
      const statusCode = apiError.response?.status;

      onLog({
        level: 'error',
        message: `Fortnox Archive API error (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`Fortnox Archive API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    const uploadedFile = uploadResponse.data as FortnoxArchiveResponse;

    // Store file information in variables
    const variableName = step.variableName || 'var-fortnox-file';
    const fileResult = {
      fileId: uploadedFile.Archive.Id,
      filename: uploadedFile.Archive.Name,
      size: uploadedFile.Archive.Size,
      path: uploadedFile.Archive.Path,
      fullResponse: uploadedFile.Archive
    };

    variables[variableName] = fileResult;

    onLog({
      level: 'info',
      message: `File uploaded successfully: ${uploadedFile.Archive.Name} (ID: ${uploadedFile.Archive.Id})`,
      stepId: step.id
    });

    return {
      success: true,
      variables: {
        [variableName]: fileResult
      }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Error uploading file to Fortnox: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}
